#!/usr/bin/env python3
"""
个人信息管理模块依赖安装脚本

此脚本用于安装个人信息管理模块所需的依赖包，
支持选择性安装云存储SDK。
"""

import subprocess
import sys
import os
from typing import List, Dict


class DependencyInstaller:
    """依赖安装器"""
    
    # 必需依赖
    REQUIRED_PACKAGES = [
        "Pillow==10.4.0",
    ]
    
    # 可选依赖（云存储SDK）
    OPTIONAL_PACKAGES = {
        "oss": "oss2==2.18.4",           # 阿里云OSS
        "cos": "cos-python-sdk-v5==1.9.30",  # 腾讯云COS
        "qiniu": "qiniu==7.12.1",        # 七牛云
    }
    
    # 开发依赖
    DEV_PACKAGES = [
        "pytest==8.3.2",
        "pytest-asyncio==0.23.8",
    ]
    
    def __init__(self):
        self.installed_packages = []
        self.failed_packages = []
    
    def run_command(self, command: List[str]) -> bool:
        """执行命令"""
        try:
            print(f"执行命令: {' '.join(command)}")
            result = subprocess.run(
                command, 
                check=True, 
                capture_output=True, 
                text=True
            )
            print(f"✅ 成功: {result.stdout.strip()}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 失败: {e.stderr.strip()}")
            return False
    
    def install_package(self, package: str) -> bool:
        """安装单个包"""
        command = [sys.executable, "-m", "pip", "install", package]
        success = self.run_command(command)
        
        if success:
            self.installed_packages.append(package)
        else:
            self.failed_packages.append(package)
        
        return success
    
    def install_required_packages(self):
        """安装必需依赖"""
        print("\n🔧 安装必需依赖包...")
        print("=" * 50)
        
        for package in self.REQUIRED_PACKAGES:
            print(f"\n安装 {package}...")
            self.install_package(package)
    
    def install_optional_packages(self, storage_types: List[str]):
        """安装可选依赖"""
        if not storage_types:
            print("\n⏭️  跳过云存储SDK安装")
            return
        
        print(f"\n☁️  安装云存储SDK: {', '.join(storage_types)}")
        print("=" * 50)
        
        for storage_type in storage_types:
            if storage_type in self.OPTIONAL_PACKAGES:
                package = self.OPTIONAL_PACKAGES[storage_type]
                print(f"\n安装 {package}...")
                self.install_package(package)
            else:
                print(f"❌ 未知的存储类型: {storage_type}")
    
    def install_dev_packages(self):
        """安装开发依赖"""
        print("\n🧪 安装开发测试依赖...")
        print("=" * 50)
        
        for package in self.DEV_PACKAGES:
            print(f"\n安装 {package}...")
            self.install_package(package)
    
    def print_summary(self):
        """打印安装总结"""
        print("\n" + "=" * 60)
        print("📊 安装总结")
        print("=" * 60)
        
        if self.installed_packages:
            print(f"\n✅ 成功安装 ({len(self.installed_packages)} 个):")
            for package in self.installed_packages:
                print(f"  - {package}")
        
        if self.failed_packages:
            print(f"\n❌ 安装失败 ({len(self.failed_packages)} 个):")
            for package in self.failed_packages:
                print(f"  - {package}")
            print("\n💡 建议:")
            print("  1. 检查网络连接")
            print("  2. 升级pip: python -m pip install --upgrade pip")
            print("  3. 清理缓存: pip cache purge")
            print("  4. 手动安装失败的包")
        
        print(f"\n📈 总体成功率: {len(self.installed_packages)}/{len(self.installed_packages) + len(self.failed_packages)} ({len(self.installed_packages)/(len(self.installed_packages) + len(self.failed_packages))*100:.1f}%)")


def get_user_choice() -> Dict[str, any]:
    """获取用户选择"""
    print("🚀 个人信息管理模块依赖安装器")
    print("=" * 60)
    
    choices = {
        "storage_types": [],
        "install_dev": False
    }
    
    # 选择云存储SDK
    print("\n☁️  请选择需要的云存储SDK (可多选，用空格分隔):")
    print("  1. oss    - 阿里云对象存储")
    print("  2. cos    - 腾讯云对象存储")
    print("  3. qiniu  - 七牛云存储")
    print("  4. all    - 安装所有云存储SDK")
    print("  5. none   - 不安装云存储SDK (仅使用本地存储)")
    
    storage_input = input("\n请输入选择 (例如: oss cos 或 all 或 none): ").strip().lower()
    
    if storage_input == "all":
        choices["storage_types"] = ["oss", "cos", "qiniu"]
    elif storage_input == "none" or storage_input == "":
        choices["storage_types"] = []
    else:
        storage_types = storage_input.split()
        valid_types = ["oss", "cos", "qiniu"]
        choices["storage_types"] = [t for t in storage_types if t in valid_types]
    
    # 选择是否安装开发依赖
    dev_input = input("\n🧪 是否安装开发测试依赖? (y/N): ").strip().lower()
    choices["install_dev"] = dev_input in ["y", "yes", "是"]
    
    return choices


def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("❌ 错误: 需要Python 3.8或更高版本")
            sys.exit(1)
        
        # 获取用户选择
        choices = get_user_choice()
        
        # 创建安装器
        installer = DependencyInstaller()
        
        # 确认安装
        print(f"\n📋 安装计划:")
        print(f"  - 必需依赖: {len(installer.REQUIRED_PACKAGES)} 个")
        print(f"  - 云存储SDK: {len(choices['storage_types'])} 个 ({', '.join(choices['storage_types']) if choices['storage_types'] else '无'})")
        print(f"  - 开发依赖: {len(installer.DEV_PACKAGES) if choices['install_dev'] else 0} 个")
        
        confirm = input("\n确认开始安装? (Y/n): ").strip().lower()
        if confirm in ["n", "no", "否"]:
            print("❌ 用户取消安装")
            sys.exit(0)
        
        # 开始安装
        print(f"\n🚀 开始安装依赖包...")
        
        # 安装必需依赖
        installer.install_required_packages()
        
        # 安装云存储SDK
        installer.install_optional_packages(choices["storage_types"])
        
        # 安装开发依赖
        if choices["install_dev"]:
            installer.install_dev_packages()
        
        # 打印总结
        installer.print_summary()
        
        # 安装后提示
        if not installer.failed_packages:
            print(f"\n🎉 所有依赖安装完成!")
            print(f"\n📖 接下来的步骤:")
            print(f"  1. 配置存储方案 (参考: docs/个人信息管理模块部署指南.md)")
            print(f"  2. 运行数据库迁移: aerich upgrade")
            print(f"  3. 启动应用测试功能")
            
            if choices["install_dev"]:
                print(f"  4. 运行测试: pytest tests/test_profile_management.py -v")
        else:
            print(f"\n⚠️  部分依赖安装失败，请手动处理失败的包")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print(f"\n❌ 用户中断安装")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程中出现错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
