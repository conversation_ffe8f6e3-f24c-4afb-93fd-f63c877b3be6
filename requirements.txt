# Base dependencies
fastapi==0.111.0
uvicorn==0.29.0
loguru==0.7.2
pydantic==2.8.2
PyYAML==6.0.1
ruamel.yaml==0.18.6
requests==2.31.0
httpx==0.27.0
jieba==0.42.1
beautifulsoup4==4.12.3
python-dotenv==1.0.1
cachetools==5.3.3
pydantic_core==2.20.1
pyee>=13,<14
PySocks==1.7.1
pytz==2024.1

# Async and Database
# Use Celery 5.x, the modern version
celery[redis]==5.4.0
# asyncpg and psycopg2-binary are for PostgreSQL
asyncpg==0.29.0
psycopg2-binary==2.9.9
tortoise-orm==0.21.3

# telethon - uncomment if you are using telegram crawler
telethon==1.34.0

# For data processing from excel
pandas==2.2.2
openpyxl==3.1.3
tqdm==4.66.4

# For data processing from excel
lxml==5.4.0
certifi==2025.6.15
curl_cffi==0.10.0
nest-asyncio==1.6.0
aerich==0.7.2
playwright==1.52.0
playwright-stealth==1.0.6
gevent==24.2.1
greenlet==3.2.3

meilisearch>=0.28.0

# Authentication and Security dependencies
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.9
jinja2==3.1.4
email-validator==2.1.1

# meilisearch_listener依赖服务
aiohttp==3.9.1
psutil==5.9.5

# Profile Management Module Dependencies
# Image processing for avatar upload
Pillow==10.4.0

# Cloud storage SDKs (optional, install as needed)
# Alibaba Cloud OSS
oss2==2.18.4

# Tencent Cloud COS
cos-python-sdk-v5==1.9.30

# Qiniu Cloud Storage
qiniu==7.12.1

# Testing dependencies (for development)
pytest==8.3.2
pytest-asyncio==0.23.8
