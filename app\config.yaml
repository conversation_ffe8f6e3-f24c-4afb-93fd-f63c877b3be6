# 应用程序基本信息配置
app:
  name: pan-so-backend  # 应用名称
  version: 1.0.0        # 应用版本号
  debug: false           # 是否开启调试模式

# 数据库配置
database:
  # 使用标准的数据库连接URL，这是 Tortoise ORM 和 Aerich 的首选方式
  url: "postgres://pan_so_user:Wsk1998107.@localhost:5432/pan_so_db"
  connections:
    default:
      engine: tortoise.backends.asyncpg
      credentials:
        host: "localhost"
        port: "5432"
        user: "pan_so_user"
        password: "Wsk1998107."
        database: "pan_so_db"
  apps:
    models:
      models:
        - "app.models.resource"
        - "app.models.submission"
        - "app.models.feedback"
        - "app.models.user"
        - "app.models.help_request"
        - "app.models.config_models"
        - "aerich.models"
      default_connection: "default"

# 日志配置
logging:
  level: "INFO"  # 日志级别
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"  # 日志格式
  filename: api.log  # 日志文件名

# 企业微信推送配置
wx_push:
  corp_id: ww85eb6097649bfa4d  # 企业ID
  corp_secret: _uQAPvqzla0FMlPx-QZS0jFFQ8AUWQ3J8H8o86ysSPQ  # 企业密钥
  agent_id: "1000002"  # 应用ID
  to_party: "2"        # 接收消息的部门ID
  app_token: AT_xxx    # 应用Token
  uids:                # 接收消息的用户ID列表
    - UID_xxx

# API服务配置
api:
  host: 127.0.0.1  # 监听地址
  port: 9999     # 监听端口
  debug: false   # 是否开启调试模式
  max_concurrent_requests: 10  # 最大并发请求数
  cors_origins:  # CORS允许的源
    - "*"
  allowed_origins:  # 允许访问的源列表
    - http://localhost:3000
    - http://localhost:8080
    - http://127.0.0.1:3000
    - http://127.0.0.1:8080
    - https://pansoo.cn

# 百度网盘账号配置
baidu_accounts:
  - name: 默认账户  # 账号名称
    #cookie: XFI=1ea905e6-3b3c-1c57-5607-00abedc48701; XFCS=5DEAA7A7E0F4FD976832D87BADC91766B33E4E42FAE95EBD1D49BBAC44C2DFBF; XFT=xu0YC9Ue2cYPE+yyekJzNYaR3MzxuQwrJM1CtS8/m9g=; BIDUPSID=ACE4081D39CBAF562BEF206964008D99; PSTM=**********; newlogin=1; PANWEB=1; MAWEBCUID=web_HCkdWxTfSJxowNETACtnvXqXHVuwYjnucBrgBAFCPpPxpNHByR; SCRC=fba9943368218bd37742e7bb4a884c73; BAIDUID=7D350ABB46C537BBD9892E12D0F6975F:FG=1; BDORZ=B490B5EBF6F3CD402E515D22BCDA1598; BDCLND=6inZcaybpjWZC5Z7IVv7FzbwO2DmNCJgN2EZkiYM4cA%3D; H_PS_PSSID=60271_62325_63143_63325_63558_63583_63693_63724_63719_63881_63892_63916_63952_63948_63275_63992; H_WISE_SIDS=110085_632167_644660_646541_646543_651904_652167_652586_653265_646558_653822_655339_655444_655722_655636_656099_656094_656401_656660_656506_656704_655068_654948_657009_657124_657108_657055_656456_641262_657521_657518_657591_657872_657999_657969_658029_658031_658033_658103_658123_655951_658255_658450_658451_658473_656754_657854_656076_655920_658531_658539_658575_658572_656627_658592_658588_658085_658580_658583_658669_658666_658637_658585_658788_658752_658881_658917_658922_658927_658993_659090_659094_659030_659247_659239_659305_659459_655415_658532_651759_659474_659580_658757_659587_659601_659628_659632_659634_659647_659659_658258_659726_659658_659730_659742_8000091_8000109_8000142_8000158_8000161_8000162_8000171_8000178_8000203_8000209; BAIDUID_BFESS=7D350ABB46C537BBD9892E12D0F6975F:FG=1; BA_HECTOR=0l01a0808g0ha4al05000k840k0h0l1k6qef325; ZFY=jzWxTqDLlcpvqIHx2FRZY4tZ:Ai1rpLBlnT4aIAxVwZM:C; BAIDUID_REF=7D350ABB46C537BBD9892E12D0F6975F:FG=1; BDPASSGATE=IlPT2AEptyoA_yiU4SOA3lIN8eDEUsCD34OtViBi3ECGh67BmhGq4qJHVVzLTSetBzTMdI3JmcldjijsQmFuirMen_cBuEcadySccKq05KTwSa62-rss_2zgGFkUwuDPbRhL-3MEF3VEVFpbevHGj3wigBSnsQlsecbNs6LWmMr81TyG3mHQx6iIK8RkLGPYKr_C94rSjE6KLl_WZOmtT6jebCJHO7As70aOb2YiAvfdyEoXCurSRvEA0nXQ37J2_4K-1PW79cC8AE9SzZ2YSkUtdEiV5tD6MSgsAM4UlK6DSKzePcCQUiqG_swka0PbLQdWKQ3zmtkGDUYX6XYlJHVj_NnFFTHrDjUNYMGGiBjZCXwVqlOEIw4xqptfE0LpW4-0LBpDYit-hS7fy3CmpSr2HwPgi0FvMtoMInOmzJl4bn63Gm4K6Hqzc7Jv-oahWVaG1SbdArnlLWNKuuKyJaXaVKr82VEhqS_XgECavkjiZh4aNMR7kzgCwnV7TWiB9e7WVpXjUwTtyNlfwrXqvszDu9SUqzaTw6uCLdyyNAAKVYI0qBJ4CmjJzGy_eXx_O4G6v7z9IUjZj5RMwtyIjyZnlPA01NJQOs4rBc1LwcSIkWwV0yYto_Sz1wZD2-rL9AZT_dWkY98LAobVVmVRgrtA2V4vIJTzvt4wE52TBz4p1HXh0KhpxUF8h71WOQ4AZ5buavsEUVuCbQYjL2qip7diFOEjEi25v8zY3J8NCRn7y4REqUEut0or0oat104XwIk7oagqUyCuyq0ZIai7hZgZy8yJgQZBV0q3X7vk8FKyIBEq-nnzEFzbxK; H_WISE_SIDS_BFESS=110085_632167_644660_646541_646543_651904_652167_652586_653265_646558_653822_655339_655444_655722_655636_656099_656094_656401_656660_656506_656704_655068_654948_657009_657124_657108_657055_656456_641262_657521_657518_657591_657872_657999_657969_658029_658031_658033_658103_658123_655951_658255_658450_658451_658473_656754_657854_656076_655920_658531_658539_658575_658572_656627_658592_658588_658085_658580_658583_658669_658666_658637_658585_658788_658752_658881_658917_658922_658927_658993_659090_659094_659030_659247_659239_659305_659459_655415_658532_651759_659474_659580_658757_659587_659601_659628_659632_659634_659647_659659_658258_659726_659658_659730_659742_8000091_8000109_8000142_8000158_8000161_8000162_8000171_8000178_8000203_8000209; SE_LAUNCH=5%3A1751988722; BDUSS=VGWkJ1YUc3VmpSRi1wbEZsRFNvNmpkRVM4UkEzflVlSjRmZFQwfk5zb1F6WlJvSVFBQUFBJCQAAAAAAAAAAAEAAABcQDE7t6K49rfiAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABBAbWgQQG1oam; BDUSS_BFESS=VGWkJ1YUc3VmpSRi1wbEZsRFNvNmpkRVM4UkEzflVlSjRmZFQwfk5zb1F6WlJvSVFBQUFBJCQAAAAAAAAAAAEAAABcQDE7t6K49rfiAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABBAbWgQQG1oam; csrfToken=Nx_QI9GEBGd0Ypjxl-O_gq9d; STOKEN=4c3894c76dce889a5786a625e2b7b7ceccf4e4bb51ca179bf49198831c853327; PANPSC=3103613907842449373%3AnHZOtuqy9auShSSWo%2BObvlcS2d9ns3O5C61tf8CKQkiBificvaXpugXQ225L75l2z81ttRoL0tCAzpMjKfHvSk%2B1wF0F%2B2es5qyWi6dfu0ttcQe%2BbgwEaTCyYQOwAMBA4CvFohGLzm45E0pwGYsGtKMXQ%2F6ST4RzYqZdy9zZ13lsfZ87u8WyL9XDsRvmnT3u0S%2BYIb4pggTsgpU1IERfCnAQa%2FUi3J%2Ft8IKldUcUtdCOhXbRLWybyCQM5yqirZE0y1UKHR%2BcUDokqtDb6%2BO1Uw%3D%3D; Hm_lvt_182d6d59474cf78db37e0b2248640ea5=**********,**********,**********,**********; Hm_lpvt_182d6d59474cf78db37e0b2248640ea5=**********; Hm_lvt_7a3960b6f067eb0085b7f96ff5e660b0=**********,**********,**********,**********; Hm_lpvt_7a3960b6f067eb0085b7f96ff5e660b0=**********; HMACCOUNT=5EF42A1F6D9884B7; ndut_fmt=5C9042882624FFECB77BEA4A87391FC1282FA75136E728F2F8DEB998AC9DC681; ab_sr=1.0.1_ZjVkOWIxMjIyYzg1MjA1OGU3ODlhYjc2Mjg4MjJkMGMzYzdiZmQyMWM2MzNiZTY0ZjEzMzdmNjM4MTc0YjI5YWE0N2NlYjE0ZTljYTJjOWEwNjQwZTNmMTIxNTc0ZjIyNDJmNTQwMzQwOTU1MjE1MzcwN2QyYTkwNTBjMjg5NTYzM2E2YmQwYzZlZGEwNDY4N2E2MWVkZGQ3YTJiYmI0NzI5YjFmZWQ4NDk3Mzg3MGExZmNkMDgyNmFkMjczMmJkMGZlY2U4Y2ZlMGE0MDBlZTgwMTA3YjEyZjk3ZjQ4OWE=
    cookie: XFI=91540c54-b48e-c421-52e9-f6ee205bb18c; XFCS=4BB6A1C6272B43471A8F7F4C7ED659FAEA9DBF1957B961257C258BA039467B8A; BAIDU_WISE_UID=wapp_1751209500915_437; __bid_n=197bc38bc0b686478ebb74; BIDUPSID=D243DD45A5C57AAEFE5642060D6265ED; PSTM=**********; BAIDUID=9B0DACDA1C80119405846085B4FB6E91:FG=1; BAIDUID_BFESS=9B0DACDA1C80119405846085B4FB6E91:FG=1; MCITY=-218%3A; newlogin=1; PANWEB=1; BDCLND=tMgw2FNteGoUJWJGDQJYi2%2Fubybpv7rHqvOVLPXSN2o%3D; ZFY=gOg9IebT4Bh0T7OfdHTykuQDadzIzyVCchopzk9qpoU:C; RT="z=1&dm=baidu.com&si=e9460ddd-07a0-4cf6-8f77-23b7402b9f4c&ss=md32knph&sl=1&tt=ja&bcn=https%3A%2F%2Ffclog.baidu.com%2Flog%2Fweirwood%3Ftype%3Dperf&ld=1co&ul=1mg&hd=1mk"; Hm_lvt_7a3960b6f067eb0085b7f96ff5e660b0=1751891742,1751895013,1752495525; BDUSS=ViS1BZdUg2WG43T3lxUUtRZ21NbU5LaUJkemRBfmVuYmdGOX5XN2JZa1VnNXhvSUFBQUFBJCQAAAAAAAAAAAEAAAAANQG2wu27r8zatcTH17DWNQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABT2dGgU9nRobE; BDUSS_BFESS=ViS1BZdUg2WG43T3lxUUtRZ21NbU5LaUJkemRBfmVuYmdGOX5XN2JZa1VnNXhvSUFBQUFBJCQAAAAAAAAAAAEAAAAANQG2wu27r8zatcTH17DWNQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABT2dGgU9nRobE; H_PS_PSSID=62325_63141_63326_63808_63819_63881_63916_63901_63934_63950_63947_63275_63991_64011_64013_64026; BA_HECTOR=0k05842hah24a1aha0852gag8081861k79tko24; BDRCVFR[feWj1Vr5u3D]=I67x6TjHwwYf0; PSINO=3; delPer=0; BDORZ=B490B5EBF6F3CD402E515D22BCDA1598; BRAND_LANDING_FROM_PAGE=2231_0_1752495776; BCLID=8027342924978621265; BCLID_BFESS=8027342924978621265; BDSFRCVID=YdkOJexroGWdkprsJXUbEHtYodNbUdrTDYrEjGc3VtzSGYLVvk4cEG0Pts1-dEub6j30ogKKLgOTHUQgFmL2vocqExJ1J3KydXRLtf8g0M5; BDSFRCVID_BFESS=YdkOJexroGWdkprsJXUbEHtYodNbUdrTDYrEjGc3VtzSGYLVvk4cEG0Pts1-dEub6j30ogKKLgOTHUQgFmL2vocqExJ1J3KydXRLtf8g0M5; H_BDCLCKID_SF=tbC8VCDKJKD3qbjkq45HMt00qxby26nk5I39aJ5y-J7nSnbcKb55ylkA-4o7-pcy5goa-Rb-QpbZql5FQP-53R0h0PJkWp5q2bn2Kl0MLPb5hj6gQJoDj4TyDMnMBMnr52OnaU5l5hQV8KoChPbaQJkpDtCeBbcbyNrrLnLy5KJYMDcnK4-XDTJLDGoP; H_BDCLCKID_SF_BFESS=tbC8VCDKJKD3qbjkq45HMt00qxby26nk5I39aJ5y-J7nSnbcKb55ylkA-4o7-pcy5goa-Rb-QpbZql5FQP-53R0h0PJkWp5q2bn2Kl0MLPb5hj6gQJoDj4TyDMnMBMnr52OnaU5l5hQV8KoChPbaQJkpDtCeBbcbyNrrLnLy5KJYMDcnK4-XDTJLDGoP; csrfToken=rPJz-_DZ8sEwc5ZKnDNiI38E; STOKEN=a513b7001ce67c302ea74bf06411aaf7e2bf82e0927b1dc6a35945326a0902d5; PANPSC=1839233676561088849%3AnHZOtuqy9asu3eSbE71zqFcS2d9ns3O5C61tf8CKQkjgUyjjQmBAF6UY4cQrI%2BJ7z81ttRoL0tCAzpMjKfHvSpx4FKAbwUKXrCMp42ERXP42jyb7JodIivKBB5%2BWSCTpQJwNd%2FgEiZyohISDxds87pcRI7EpduIhYqZdy9zZ13lsfZ87u8WyL9XDsRvmnT3u0S%2BYIb4pggT%2BgtL0gxNFSof%2FnVkU05fi%2FDuUzs8tV%2BzcTSJNmOle%2FPkeTx3LroVudiV%2FP%2FkmZQgIISe419aBI7VgOulmQnuHELeBAX%2BWAuuzeOS4a%2BpKdFnRb%2FASyjbBkrFWWqsgPEySsVZaqyA8TA%3D%3D; Hm_lvt_182d6d59474cf78db37e0b2248640ea5=**********,**********,**********,**********; Hm_lpvt_182d6d59474cf78db37e0b2248640ea5=**********; HMACCOUNT=2CD1FA3391D80550; ndut_fmt=568CB6A69C0BCB2DCE86364D64AA68F7D3E22D8ACC9FA9AC6FC0EEB7B2DB711F; ab_sr=1.0.1_ZTljZDI0NDM4OTFjN2IxODY4ZjY2MmZjOTlkNjQ2OTM0Mjk0ODk5Yjc4OGNhMzNkMTk4ZWFjM2U3MGEwM2MzYzM1MmI3YzM5ZDlhZDk3YzVmMWVkNzNjMDIyNjliYmFhYzZkM2FiNDFmYWU4ZmZiYTRiZTk5YTQyNmI4YjQ2NGM5NDZlOWYxZTcxYzUxMmViZDJjOThjYTY0OGEzOTY5MmY5YWEyYTRhNTU2NTc3OTRmMDM4YzMyMjY1YmRkZmYzNTVlY2I1OWU3MTEzMGU4NGE3M2UwYWJiZGM1M2MxMTBhMDM5ZjcxNDBmMjdkZjFiYWFhYTJjOWQ1MzIwZTBjZg==
    username: 发个封  # 用户名
    phone: 139*****773  # 手机号
    remaining_size: *************  # 剩余空间(字节)
  - name: 账户2  # 采集入库信息
    cookie: XFI=********-2f1d-5752-e4aa-d3f51fb211a8; XFCS=F4744DFF0B7A9AE1BCC77743BB4BAEDC676E4B43D425EA5AB9A3BA48E8AEF2A9; XFT=9+ZqcjpIT0wdlTIqOT48NRrpbv2fCVbofrtD2NOMqAM=; csrfToken=yYr3OJ9sT2fDcAnk_S-HZiIs; BAIDUID=990A6441E5C275647EF11951EBC09C2C:FG=1; BAIDUID_BFESS=990A6441E5C275647EF11951EBC09C2C:FG=1; Hm_lvt_7a3960b6f067eb0085b7f96ff5e660b0=**********; Hm_lpvt_7a3960b6f067eb0085b7f96ff5e660b0=**********; HMACCOUNT=0654CFD55FB48327; ndut_fmt=C50DCF5CF17AE4CC6C1A5E6BB181D8DFD3C9D05DC158BFC734FCDF82DE6CB195; ab_sr=1.0.1_ODYwZTI2MTM4Nzg2OWMzYTcxNTczNjNiM2UwMmYxNThkYmJiY2ZkNDU4NjA3NjM2MmU5NjExOTk3ODFiNzNjMjg0NzI3ZjkyMThjOGE4MzMyNDY0OTQ0MDhkYWJlYmU4MDIyNjlmYmM4OGIwMjMxOThlZjE4YTQ4MGU3MGZjNTE5NGRiYjYxMjZhNTg2OGE5NGJkOTU3ODZjNDIxYjI2NA==; PANWEB=1
    username: xxx
    phone:
    remaining_size:

# 夸克网盘账号配置
quark_accounts:
  - name: 默认账户       # 账号名称
    #cookie: ctoken=fnOJzWA9jPexxvSBxzFbhSl3; b-user-id=85cee25e-bcb5-a569-2cb1-5c042aae058e; __wpkreporterwid_=57452b81-d489-4862-be47-f4dac67d7097; grey-id=f301c77c-0231-6f00-941e-fffb27575612; grey-id.sig=Dx0m2TfCPfTR_zhmbWvSnZCwpE3xCnqp0DvYhf4DX5s; isQuark=true; isQuark.sig=hUgqObykqFom5Y09bll94T1sS9abT1X-4Df_lzgl8nM; _UP_A4A_11_=wb9cb1ab1bac40a8b21fb43f57b75afb; _UP_D_=pc; _UP_F7E_8D_=pCQi2KLo78bEPFveTIflgVRWf5QupjLertOFmrP%2F5Q50XQzuJBCvaVLDDVA7JqQdKWRvQZKRMx2pcUe9jZI%2Bxpr1WkQL6QwPm1P4sD4fnHqwPMpd0%2B7BZarlx5I0Wl5nDsG05Cf8pRZwZDzB3oYCMpJnLMnpMbZs6RoHEkf8wVft05k05ngkGyHsIakZbjBMQlwW1OikDv4IkxR7AYyuKAdBSgDxBfRiRK7XQvKz5yZjP7TNzwlknl8IaiP9jBN5rPsU17bDenKj%2Bhm3mVYTBSQI4DlGUbtPZ%2FGA1kMkAh0jtD9ufGqk79Uxi0xHOqsZoAg3J36dS9adhc5VTnv%2BjWSpj%2B0v9aqQoAg3J36dS9ZU25CTU1P0WsIDEnp%2BzT9l6QFa%2Funqf%2BpNktF%2FskRKCJPGNxq9uNZW; __pus=24cda183ea5edaa06a454a2f39d3b01bAAQk+xd85/Gu5XnoeZeSJiGX+QOrP1thuHIcCXkAYfBTeAZCjH3ttFuDSkh97KkqPQVaYBxM6mBL9zx5+n+Z6rCx; __kp=9bfbd850-66e6-11f0-9535-4b2a4d08f7c5; __kps=AATPvVcBbTbzlrbu/iRo2+Yi; __ktd=HTlKcckt6488iGlEIUnBog==; __uid=AATPvVcBbTbzlrbu/iRo2+Yi; web-grey-id=8cb57f70-161d-f85c-bf2e-739decedc9d0; web-grey-id.sig=I7Avch6V2gv3y9wXj52z3W5N-19oWWGt1ggNePN7hSE; __puus=1f624bcf97aa2c1303594ce756e1e5a6AASZNVddkTY4c8xuoXwB31c2bBjVbW4ls2n76249/+XPbT/1Hi9qe9Y943rccBcbV+tL1494Dy8YxvdpFiVb549XMrTwNoQ7QBQhxEn9dFXJv+j726RyzaD56g3RDnw034acuxdEI6nNkxtbHR0BIkTv0qe6a96zvgcNF6ecFmskntK1x+WVzVRbzVQznpjHO41pi9SRUly9o4NfYYKbO45n
    cookie: ctoken=PeCADHS-vPsdRsrq5IGnWQId; b-user-id=647a93b7-9159-6236-543b-b38f58090ad0; grey-id=5025c172-9371-a048-162a-dc3b8dabef15; grey-id.sig=dJaX0yuVshZSIsyBHnssxwBVMMzQmEe1k6d8hVmcdxs; isQuark=true; isQuark.sig=hUgqObykqFom5Y09bll94T1sS9abT1X-4Df_lzgl8nM; __wpkreporterwid_=c733be72-a4fd-4ab9-83b5-5616f8e89f00; _UP_A4A_11_=wb9cb18ba2e545349dba0b2f16ca0dc6; _UP_D_=pc; _UP_F7E_8D_=0z44HdIBxZYkZ5f9VFc3Yme2bdV8c7WZCrfGWfu71XtaLo7v5X%2FOK38BX2Jsujy9BRktcpT0VpaIbELvsKV6N7Qey3LIThbBZYYBSlEw%2FhFvnlMM%2FoU51YjKLHiThZj4jaLLO9AVQUwLNou9CehuHWlMWS1rXVfbZXRHxH%2Fc5ejTk%2FYYBSlxZ2dk3A9f%2FCMRONTz2BI%2FOPrpV4OIH2rmX0VF6jsQF6Mjj3zd%2Fr57GvWdSoxLVcoYHq0SZVsJ3x3FqryYkg0yaJQATvkG7Ome7SQ6eNGsMWaTpmlSmANT6c1ANK4v8a%2BQ2MHioDqQO2Iml2%2B2H3zLzMHfdzjPhIn9KLnVmIzq%2FTTa5GH40CMVkY7o01GI0ATtUO91YJ0UKEIX39siXgbW2M9npW%2F75YhAoYvCphdd0pvjAl37%2Fu6rZ9%2BAuY0UFHP8TZpAjXZfykY6Bw2eJBV4FBFSwLwKW%2Fv64A%3D%3D; _UP_30C_6A_=st9cb62013474xumjjw6ol1fhzhre1w7; _UP_TS_=sg1579a10d3502404f0b6338b67d1f33aae; _UP_E37_B7_=sg1579a10d3502404f0b6338b67d1f33aae; _UP_TG_=st9cb62013474xumjjw6ol1fhzhre1w7; _UP_335_2B_=1; __pus=b64c15384832cf5785f62504d9a08095AASFin2krqOcNcgGxUFPgHq9Xd7hjYyvQvzPbwctxpE2tFbb9BsX3X3LB6x8DKTPkDQiINLxFD/7tCRr+yNZsGOh; __kp=21ce72a0-5cd6-11f0-b66c-2b53e4bd3591; __kps=AATChjkRVBmqTtVXLhHlJgM9; __ktd=mARb5jkOu+dujr7EZ8Raig==; __uid=AATChjkRVBmqTtVXLhHlJgM9; web-grey-id=b62e1fa4-8f40-9eda-acff-51cdba3fdac7; web-grey-id.sig=Ak-ytklyggFHTPOr7NC2qwJj_vm1m4pOQPuFepAu58o; tfstk=gD6Ee2XEdJeUGDuDYZpr7FMcbc9pELzftTTWqgjkAeYHApsl_gs4v3_CqU5ySaEKdp4KEUjAWBThPpgzUNJHw6TWRUJP2Zr_l-wfvMpJIrabhVi_BjJvxUDoPd0M0L-H2Td3CMpJEPadhaRCvwQ1_BpHElJM4H0kEYbkSdY9230HqvqNjFLMqHvoEdDM03Lo-avubGY9qLYlrBqNjFKkEUcJZ3xUpeA32SFVQIyNRB8cxFkVX9KnGEyv8YDhQHSwoMcmEYXwYBWMGy27HB1lDIBC48k9di5MInsLLAJcxn7B5Z2mQdj1jaOA6zHX6NWyM9AIqXjNUeRcKCoShiJwqaAP6rkXYp1wg95L2Vjhle5Dpgi4RGRV_IKMs0lHdssfeIX3LqL6M37B5Z2mQdAl4m0JjFkFe6uoZBx9bEZabusEp1EroE9-wbdiBh8b0gh-wBx9bEZabbhJsAKwloSR.; __puus=f6a9f6649b09e5bb60c768eadf258482AAT24j4ot8fGnZXtLq0vZkWO3iNj99dkawvcA7mxmMyiOXFnZxUWAbzTN4jF1XdGdb74ZgZTHhyUesnvKQOKPlUY7cyfP87LL2dIb9QCmjCWhLVosmEUDbmjy6iCU0+FBpxqIQQuEph5Fvpfb27fDGsXYt/NvutfnqqsahyEnogWMs8k5zSLt89GbDbz0TRcrg3WmGjgKq6AU/F6OClkM0EU  # 账号Cookie
    nickname: 积极进取的白兔  # 昵称
    remaining_size: *************  # 剩余空间(字节)


#迅雷网盘账号配置
xunlei_accounts:
  - name: 默认账户  # 账号名称
    phone: 139*****773  # 手机号
    authorization: Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IjEzNTRhMDBlLTFiNTQtNGI4Mi1hM2I1LTJiODUxMjY0YmMwZSJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.g8gulWuX4IEPlOOP_64NqQxauwaq27UBc8Nd5rYZTa86Hh79m8OZrIq8dhsMpXb02N-394f6fioS6_WfV-_NS-ABA81L5iKBXNHrP-6iUgGUEpYyWC5X3SxE8my7DjeKYXP_bzX1RTpCNp5dhue2NvwYaas9fniSiLbPwTcYwZaLUJXEfhyktetwIHE5XY8Lz91DDbD5U_cZbfTZgfx9CuY38oFiNMteLKb0KEv7vIsNHqDACcJqoma7tzCLVXKZaSMDEWp1K_sX3YQ-Tpk7emXmtp7yyx0LC5WCOFWbQIwrObPP1SNwdX2NrEZ2IHgYelaI_kdUJbcH---sbbJefg
    x_client_id: Xqp0kJBXWhwaTpB6
    x_device_id: 4a496ba4ec9565c76edaab80f8b01b74
    x_device_sn: wdi10.4a496ba4ec9565c76edaab80f8b01b74007c27f6bb8db366610168a1edeca42c
    captcha_sign: 1.8f98f5e530ea3edd5d243653c179be8e
    timestamp: "1748072676501"
    client_version: "1.91.24"
    access_token: "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    refresh_token: "a1.WZENVtYRfGgxeKYO9gnYrDn9gkV80JHjyFCnjVKqLRlYGxgREeMrT5G33RlAzA"
    expires_in: 7200,


# 缓存配置
cache:
  resource_cache:  # 资源缓存配置
    maxsize: 1000  # 最大缓存条目数
    ttl: 3600     # 缓存过期时间(秒)
  db_exists_cache:  # 数据库存在性缓存配置
    maxsize: 10000  # 最大缓存条目数
    ttl: 86400     # 缓存过期时间(秒)


# 代理服务配置
proxy_service:
  enabled: true  # 是否启用代理服务
  api_server_address: "http://uu-proxy.com/api/"  # 代理API服务地址
  token_id: "NMS3ZW3M3F"  # 代理服务Token ID
  size: 1  # 获取代理数量
  schemes: "socks5"  # 代理协议类型
  support_https: "true"  # 是否支持HTTPS
  restime_within_ms: 2000  # 响应时间限制(毫秒)
  format: "json"  # 返回格式
  retry_count: 1  # 代理失败重试次数
  timeout: 10  # 代理请求超时时间(秒)

# 网盘服务配置
pan_service:
  default_timeout: 15  # 默认超时时间(秒)
  max_retries: 2  # 最大重试次数
  retry_delay: 1.0  # 重试延迟时间(秒)
  max_concurrent_tasks: 5  # 最大并发任务数
  share_expiry_days: 1  # 分享链接有效期(天)
  use_proxy_for_parsing: true  # 是否对资源解析方法使用代理
  # 代理URL配置
  baidu_base_url_proxy: https://**************/baidu/  # 百度网盘代理URL
  quark_base_url_proxy: https://**************/quark/  # 夸克网盘代理URL
  quark_drive_pc_base_url_proxy: https://**************/quark-drive-pc/  # 夸克网盘PC端代理URL
  quark_drive_h_base_url_proxy: https://**************/quark-drive-h/  # 夸克网盘H5端代理URL
  # 直连URL配置
  baidu_base_url_direct: https://pan.baidu.com/  # 百度网盘直连URL
  quark_base_url_direct: https://pan.quark.cn/  # 夸克网盘直连URL
  quark_drive_pc_base_url_direct: https://drive-pc.quark.cn/  # 夸克网盘PC端直连URL
  quark_drive_h_base_url_direct: https://drive-h.quark.cn/  # 夸克网盘H5端直连URL

# panku8爬虫配置
panku8_crawler:
  base_url: https://www.panku8.com  # 基础URL
  timeout: 10  # 请求超时时间(秒)
  max_retries: 3  # 最大重试次数
  retry_delay: 1.0  # 重试延迟时间(秒)fa
  concurrent_limit: 6  # 并发请求限制
  use_fingerprint: false  # 是否使用浏览器指纹 (true=使用curl_cffi进行浏览器指纹伪装, false=使用标准httpx请求)
  search_pan_type: [1,2,3,4] # 默认搜索的网盘类型列表(0=全部, 1=百度, 2=夸克, 3=阿里, 4=迅雷), 可配置如[1, 2]仅搜索百度和夸克
  vip_cookie: HYPERF_SESSION_ID=5fSUfQw5W09CU2Y4tGUM4XVbATsoS3wpwMQveYK8
  enabled: false # 爬虫总开关



# esoua爬虫配置
esoua_crawler:
  base_url: "https://www.esoua.com"
  timeout: 10
  max_retries: 2
  retry_delay: 1.0
  concurrent_limit: 6
  anti_key: "O8BBFvxpkiB93LkErtIz" # 加密密钥，用于生成请求签名
  use_curl_cffi: true # 是否使用 curl_cffi 替代 httpx
  js_redirect_support: true # 是否启用 JavaScript 重定向支持
  enabled: false # 爬虫总开关

# Telegram爬虫配置
telegram_crawler:
  enable_catch_up: true        # 是否启用catch_up机制
  periodic_check_interval: 300  # 定期检查间隔(秒)，默认5分钟
  connection_check_interval: 30 # 连接检查间隔(秒)，默认30秒
  max_gap_recovery_attempts: 3  # 最大间隙恢复尝试次数
  api_id: 21907774               # 从 my.telegram.org 获取
  api_hash: "cf7e3c462e078050636304055993d3b4" # 从 my.telegram.org 获取
  session_name: "telegram_session" # session文件名
  channels:                      # 要抓取的频道用户名列表
    - -1001469109660
    - -1002258594100
    - -1002177855695
    - -1002584888101
    - -1001959723113
    - -1001674292882
  message_limit: 100             # 每次从单个频道抓取的最大消息数量
  proxy:
    enabled: true                 # 是否启用代理
    type: "mtproxy"                # 代理类型: "socks4", "socks5", "http", "mtproxy"
    host: "*************"              # 代理服务器地址
    port: 7629                      # 代理服务器端口
    secret: "ed48c5e26b2008dccece4d63a2ff3f86"                     # (MTProto) 密钥 

# kdocs爬虫配置
kdocs_crawler:
  enabled: true
  auto_post_resources: false # 是否自动发布资源
  auto_save_resources: false # 是否自动转存资源，这是自动发布的前置条件
  timeout: 15
  max_retries: 2
  retry_delay: 1.0
  cookie: reuse_session_id=edit/434428384518; xsr-cache-revision=2183; xsr-cache-fileVersion=635; weboffice_device_id=c064f94a78cc4eef63910f26a58ff7ff; weboffice_cdn=21; wps_endcloud=1; wps_sid=V02SmWLFTuM68hZ6YEq5o1LOeAMz5is00a4ab9a30043e73ce0; xsr-diffversion=3; swi_acc_redirect_limit=0; visitorid=1863928453; csrf=QimRMf5A8FHFwM6bEbbrb3Rh6QMJNbXe
  doc_urls:
    - "https://www.kdocs.cn/l/co72a28MWkmI"
    - "https://www.kdocs.cn/l/cmbapmIwVsfi"
    # 固定为app内的data文件夹,使用绝对路径
  data_file_path: "data/kdocs_data.json" 
  scrape_limit_per_doc: 40 # 每个文档的抓取数量上限, 0为不限制
  post_limit_per_run: 5 # 每次任务执行时发布帖子的数量上限, 0为不限制
  save_limit_per_run: 10 # 每次任务执行时转存的数量上限, 0为不限制

# 本地数据库搜索配置
local_search:
  enabled: true # 是否启用本地数据库搜索

# 安全配置
security:
  blocked_keywords:
    - "比特就业课"
    - "残疾反派O她凭亿近人"

# API 访问并发数限制
concurrency_limiter:
  times: 100
  seconds: 60

# Redis 配置 (新增)
redis:
  host: "127.0.0.1"
  port: 6379
  db: 0
  password: null # 如果有密码，请填写
  url: "redis://localhost:6379/0"

# Celery 配置 (新增)
celery:
  broker_url: "redis://127.0.0.1:6379/1" # 使用 Redis 的 1 号数据库作为 Broker
  result_backend: "redis://127.0.0.1:6379/2" # 使用 Redis 的 2 号数据库作为结果后端
  # 包含任务模块的路径
  include:
    - "app.tasks.submission"
    - "app.tasks.crawler_tasks" # 新增：包含爬虫任务模块
    - "app.tasks.seo_tasks"

# 新增学霸盘爬虫配置
xuebapans_crawler:
  enabled: true
  headless: true # 是否以无头模式运行浏览器，true表示后台运行，false表示会显示浏览器界面，方便调试

# 新增duanju.click短剧爬虫配置
duanju_crawler:
  enabled: true  # 是否启用duanju.click爬虫
  page_size: 50  # 每次请求获取多少条数据
  base_url: "https://www.duanju.click/api/play/quark/lates"
  headers:
    Accept: "*/*"
    Accept-Language: "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"
    Connection: "keep-alive"
    Referer: "https://www.duanju.click/"
    Sec-Fetch-Dest: "empty"
    Sec-Fetch-Mode: "cors"
    Sec-Fetch-Site: "same-origin"
    User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0"
    sec-ch-ua: '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"'
    sec-ch-ua-mobile: "?0"
    sec-ch-ua-platform: '"Windows"'
  cookies: "Hm_lvt_23c119ec9e022dea0c801bd6887b4221=**********; HMACCOUNT=5EF42A1F6D9884B7; _ga=GA1.1.*********.**********; Hm_lpvt_23c119ec9e022dea0c801bd6887b4221=**********; _ga_31MHVQYG2H=GS2.1.s**********$o1$g1$t1750833552$j60$l0$h0"  # 真实cookie

# aisoua爬虫配置
aisoua_crawler:
  enabled: true
  base_url: "https://aisoua.com/details/d3dde3ac9aa0aec5.html"
  timeout: 30
  crawl_interval: 60 # 爬取间隔，单位：秒

# SEO
seo:
  site_host: "pansoo.cn"  # 您的网站域名
  indexnow_api_key: "236919ceeb714a96b5f20cb5d262600f"  # 您从IndexNow获取的API密钥
  indexnow_key_file_name: "236919ceeb714a96b5f20cb5d262600f.txt" # 托管密钥的文件名

# Meilisearch 配置
meilisearch:
  host: "http://127.0.0.1:7700"   # Meilisearch 服务地址
  api_key: "M3lPuLTZeJeA7urKBe6YN7nAPJxBZxA7Lr7kamVXW_k"  # 管理密钥，生产环境安全key
  index_name: "resources"         # 索引名称



# cloudflare配置
# 头像存储配置
avatar:
  storage_type: "r2"  # 改为r2
  local_path: "uploads"  # 保留作为备用
  base_url: "/uploads"   # 保留作为备用
  
  # Cloudflare R2配置
  r2:
  #token:  **************************************** 
    account_id: "your_account_id"
    access_key_id: "your_access_key_id"
    secret_access_key: "your_secret_access_key"
    bucket_name: "pan-so-avatars"
    endpoint: "https://your_account_id.r2.cloudflarestorage.com"
    public_url: "https://avatars.your-domain.com"
    # 中国用户加速域名
    china_cdn_url: "https://avatars-cn.your-domain.com"
    
  # 图片处理配置
  image_processing:
    max_file_size: 5242880  # 5MB
    allowed_formats: ["jpg", "jpeg", "png", "webp"]
    quality: 85
    sizes:
      thumbnail: [50, 50]
      small: [100, 100]
      medium: [200, 200]
      large: [400, 400]